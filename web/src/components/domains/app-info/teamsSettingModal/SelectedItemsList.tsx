import * as React from 'react';
import { CloseIcon } from '@fluentui/react-icons-northstar';
import { ITeamsChatsItem } from '../../../../types/IGeraniumAttaneDB';
import useIndexedDbAccessor from '../../../../hooks/accessors/useIndexedDbAccessor';
import useTeamsChatsRepositoryAccessor from '../../../../hooks/accessors/useTeamsChatsRepositoryAccessor';

// CSS
import './SelectedItemsList.scss';

export interface ISelectedItemsListProps {
  selectedItems: Set<string>;
  onRemoveItem: (id: string) => void;
}

/**
 * SelectedItemsList
 * 選択されたチャット・チャネルアイテムを表示するコンポーネント
 */
const SelectedItemsList: React.FC<ISelectedItemsListProps> = (props) => {
  const {
    selectedItems,
    onRemoveItem,
  } = props;

  // IndexedDBアクセサーを初期化
  const [openDB] = useIndexedDbAccessor();
  const { allTeamsChats } = useTeamsChatsRepositoryAccessor(openDB);

  // 選択されたアイテムの詳細情報を取得（新しいアイテムを左側に表示するため逆順）
  const selectedItemDetails = React.useMemo(() => Array.from(selectedItems)
    .map((id) => {
      // IndexedDBのallTeamsChatsから詳細情報を取得
      const foundItem = allTeamsChats.find((item) => item.id === id);
      if (foundItem) {
        return foundItem;
      }

      // IndexedDBに見つからない場合は、最低限の情報で表示用アイテムを作成
      return {
        id,
        name: `選択済みアイテム (${id.substring(0, 8)}...)`,
        type: 'チャット' as const,
        chatType: 'oneOnOne' as const,
        countId: 0,
      } as ITeamsChatsItem;
    })
    .reverse(), [selectedItems, allTeamsChats]);

  // 選択されたアイテムがない場合は何も表示しない
  if (selectedItemDetails.length === 0) {
    return null;
  }

  return (
    <div className="selected-items-list">
      <div className="selected-items-container">
        {selectedItemDetails.map((item) => (
          <div key={item.id} className="selected-item">
            <span className="selected-item-type">{item.type}</span>
            <span className="selected-item-name">{item.name}</span>
            <button
              type="button"
              className="selected-item-remove"
              onClick={() => onRemoveItem(item.id)}
              aria-label={`${item.name}の選択を解除`}
              title={`${item.name}の選択を解除`}
            >
              <CloseIcon />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectedItemsList;
